"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";

const features = [
  {
    title: "Purpose-built for product development",
    description:
      "Next Core is shaped by the practices and principles that distinguish world-class product teams from the rest: relentless focus, fast execution, and a commitment to the quality of craft.",
    visual: (
      <div className="relative h-48 w-full">
        {/* Abstract geometric shapes representing product development */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <div className="bg-muted/20 border-border absolute -top-4 -left-4 h-24 w-32 rotate-12 transform rounded-lg border"></div>
            <div className="bg-muted/30 border-border absolute top-2 left-2 h-24 w-32 -rotate-6 transform rounded-lg border"></div>
            <div className="bg-muted/40 border-border h-24 w-32 rounded-lg border"></div>
            <div className="bg-primary/20 absolute right-2 bottom-2 h-8 w-8 rounded-full"></div>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: "Designed to move fast",
    description:
      "Built for speed and efficiency, Next Core helps teams ship faster with streamlined workflows and intelligent automation.",
    visual: (
      <div className="relative h-48 w-full">
        {/* Abstract geometric shapes representing product development */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <div className="bg-muted/20 border-border absolute -top-4 -left-4 h-24 w-32 rotate-12 transform rounded-lg border"></div>
            <div className="bg-muted/30 border-border absolute top-2 left-2 h-24 w-32 -rotate-6 transform rounded-lg border"></div>
            <div className="bg-muted/40 border-border h-24 w-32 rounded-lg border"></div>
            <div className="bg-primary/20 absolute right-2 bottom-2 h-8 w-8 rounded-full"></div>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: "Crafted to perfection",
    description:
      "Every detail is carefully considered, from the interface design to the underlying architecture, ensuring a premium experience.",
    visual: (
      <div className="relative h-48 w-full">
        {/* Abstract geometric shapes representing product development */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <div className="bg-muted/20 border-border absolute -top-4 -left-4 h-24 w-32 rotate-12 transform rounded-lg border"></div>
            <div className="bg-muted/30 border-border absolute top-2 left-2 h-24 w-32 -rotate-6 transform rounded-lg border"></div>
            <div className="bg-muted/40 border-border h-24 w-32 rounded-lg border"></div>
            <div className="bg-primary/20 absolute right-2 bottom-2 h-8 w-8 rounded-full"></div>
          </div>
        </div>
      </div>
    ),
  },
];

export default function FeaturesSection() {
  return (
    <section className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Linear-style Feature Cards */}
        <div className="grid gap-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{
                once: true,
                amount: 0.2,
                margin: "0px 0px -100px 0px",
              }}
              transition={{
                duration: 0.8,
                ease: [0.25, 0.1, 0.25, 1],
                delay: index * 0.15,
              }}
              className="group bg-card dark:bg-card/50 border-border relative overflow-hidden rounded-3xl border backdrop-blur-sm transition-all duration-300"
            >
              {/* Visual Section */}
              <div className="p-8 pb-4">{feature.visual}</div>

              {/* Content Section */}
              <div className="p-8 pt-4">
                <h3 className="text-foreground mb-3 text-xl leading-tight font-semibold">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground text-sm leading-relaxed font-normal">
                  {feature.description}
                </p>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-t from-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-5"></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
