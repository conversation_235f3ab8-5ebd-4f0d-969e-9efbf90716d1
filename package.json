{"name": "my-next-template", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "make": "next build", "go": "cross-env NODE_ENV=production node server.js", "err": "bunx tsc --noEmit && next lint", "format": "prettier --write .", "gen": "bunx prisma generate", "push": "bunx prisma db push", "mig": "bunx prisma db migrate", "stu": "bunx prisma studio", "reset": "bunx prisma migrate reset"}, "dependencies": {"@bprogress/next": "^3.2.12", "@hookform/resolvers": "^5.1.1", "@polar-sh/better-auth": "^1.0.3", "@prisma/client": "^6.10.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/nodemailer": "^6.4.17", "better-auth": "^1.2.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lenis": "^1.3.4", "lucide-react": "^0.523.0", "motion": "^12.19.1", "next": "15.3.4", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "prettier": "^3.6.1", "prettier-plugin-tailwindcss": "^0.6.13", "prisma": "^6.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@better-auth/cli": "^1.2.10", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "tailwindcss": "^4.1.10", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}